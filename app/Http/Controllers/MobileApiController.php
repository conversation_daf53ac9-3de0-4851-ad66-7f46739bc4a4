<?php

namespace App\Http\Controllers;

use App\Library\Helper\AcademicHelper;
use App\Models\AcademicWeek;
use App\Models\DisciplineRecord;
use App\Models\StudentClassAttendance;
use App\Models\Timetable;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\MobileNotification;
use App\Models\MobileDevice;
use App\Library\Repository\MobileNotificationRepository;
use App\Library\Repository\MobileApiRepository;

class MobileApiController extends Controller
{
    protected MobileNotificationRepository $mobileNotification;

    public function __construct(MobileNotificationRepository $mobileNotification)
    {
        $this->mobileNotification = $mobileNotification;
    }
    public function checkStudentCredentials()
    {
        $username = request('username');
        $password = request('password');
        // $deviceToken = request('deviceToken');

        $deviceToken = base64_decode(request('deviceToken'));

        $rd = null;
        $arr = null;
        if (\Auth::attempt(array('username' => $username, 'password' => $password))) {
            $userInfo = User::find($username);

            // $newAuthCode = uniqid();
            // $deleteAll = MobileDevice::where('device_token', $deviceToken)->delete();

            $newDevice = MobileDevice::create([
                'auth_code' =>  uniqid(),
                'student_id'    => $username,
                'device_type'   => 'ios',
                'user_type'   => 'student',
                'device_token' => $deviceToken
            ]);

            if ($newDevice) {
                $arr = array(
                    "name"      => $userInfo->name,
                    "photo"     => "https://sis.bfi.edu.mm" . $userInfo->photo,
                    "authCode"  => $newDevice->auth_code,
                    "id"        => $username,
                );
            }
            // $returnData = $username.":".$userInfo->name.":".$userInfo->photo;
            $returnData = $arr;
        } else {
            $returnData = 0;
        }
        return $returnData;
    }

    public function getNotifications()
    {
        $userArr = explode('|', request('username'));
        $notifications = MobileNotification::whereIn('user_id', $userArr)
            ->orWhere('notification_type', 'all')
            ->groupBy('notification_uid')
            ->orderBy('created_at', 'desc')
            ->take(30)
            ->get();

        $myObj = new \stdClass;
        $jsonString = null;
        $counter = 0;

        foreach ($notifications as $key => $notification) {
            $notificationTimeArr = explode(" ", $notification->created_at);
            $myObj->notificationTitle = $notification->notification_title;
            $myObj->notificationBody = $notification->notification_body;
            $myObj->notificationDate = $notificationTimeArr[0];
            $myObj->notificationTime = $notificationTimeArr[1];
            $myObj->notificationType = $notification->notification_type;
            $myJson = json_encode($myObj);
            $jsonString = $jsonString . $myJson;
            $counter++;
        }

        if ($counter == 0) {
            $myObj->notificationTitle = "No new notifications";
            $myObj->notificationBody = "We could not find any new notifications.";
            $myObj->notificationDate = " ";
            $myObj->notificationTime = " ";
            $myObj->notificationType = "all";
            $myJson = json_encode($myObj);
            $jsonString = $jsonString . $myJson;
        }
        $jsonString = str_replace("}{", "},{", $jsonString);
        $jsonString = "[" . $jsonString . "]";
        return $jsonString;
    }

    public function checkStaffCredentials()
    {
        $username = request('username');
        $password = request('password');
        // $deviceToken = request('deviceToken');

        $deviceToken = base64_decode(request('deviceToken'));

        $rd = null;
        $arr = null;
        if (\Auth::attempt(array('username' => $username, 'password' => $password))) {
            $userInfo = User::where('username', $username)->first();

            // $newAuthCode = uniqid();
            // $deleteAll = MobileDevice::where('device_token', $deviceToken)->delete();

            $newDevice = MobileDevice::create([
                'auth_code' =>  uniqid(),
                'student_id'    => $userInfo->id,
                'device_type'   => 'ios',
                'user_type'   => 'staff',
                'device_token' => $deviceToken
            ]);

            if ($newDevice) {
                $arr = array(
                    "name"      => $userInfo->name,
                    "photo"     => "https://sis.bfi.edu.mm" . $userInfo->photo,
                    "authCode"  => $newDevice->auth_code,
                    "id"        => $username,
                );
            }
            // $returnData = $username.":".$userInfo->name.":".$userInfo->photo;
            $returnData = $arr;
        } else {
            $returnData = 0;
        }
        return $returnData;
    }

    public function storeApiClassAttendance()
    {
        $timetableId = request('timetable');
        $attendance = request('attendance');
        $topic      = request('topic');

        $timetable = Timetable::find($timetableId);
        $attendanceInfo = explode("/", $attendance);
        $branchId = $timetable->branch_id;
        $academicYearId = $timetable->academic_year_id;
        $today = date('Y-m-d');
        $weekInfo = AcademicWeek::where('start_date', '<=', $today)
            ->where('end_date', '>=', $today)
            ->where('branch_id', $branchId)
            ->first();
        $currentWeek = $weekInfo->week;
        // $weekDayDate = $ah->weekDayDate($currentWeek, $timetable->week_day);

        foreach ($attendanceInfo as $key => $info) {
            if ($info != '') {
                $arr = explode("|", $info);

                StudentClassAttendance::updateOrCreate(
                    [
                        'academic_year_id'  => $academicYearId,
                        'week_day'          => $timetable->week_day,
                        'week_time'         => $timetable->week_time,
                        'subject_id'        => $timetable->subject_id,
                        'teacher_id'        => $timetable->user_id,
                        'grade_id'          => $timetable->grade_id,
                        'week'              => $currentWeek,
                        'student_id'        => $arr[0]
                    ],
                    [
                        'date'              => date('Y-m-d'),
                        'attendance_status' => $arr[1],
                        'attendance_note'   => $arr[2]
                    ]
                );
                /*      //Sending notification for absent subjects
                if($arr[1] == "absent"){
                    $userInfo = User::where('id',  $arr[0])
                        ->where('user_status', 1)
                        ->first();
                    $subjectInfo = ElectiveGrade::where('grade_id', $timetable->grade_id)
                        ->first();
                    $msj = $userInfo->name." was marked as absent in ".$subjectInfo->grade_name." class on ".date('Y-m-d');
                    $this->mobileNotification->sendSingle([
                        'title'     => 'Attendance',
                        'message'   => $msj,
                        'student'   => $arr[0],
                        'type'      => 'attendance'
                    ]);
                }
                */
            }
        }
    }
    public function storeBps(Request $request)
    {
        $repository = new MobileApiRepository();

        // Unified data structure that handles both single and multiple BPS records
        $data = [
            'branch_id' => $request->input('branch_id'),
            'case_type' => $request->input('case_type'),
            'date' => $request->input('date'),
            'note' => $request->input('note'),
            'user_id' => $request->input('user_id'),

            // Multiple format (arrays)
            'students' => $request->input('students', []),
            'items' => $request->input('items', []),

            // Single format (individual IDs)
            'student_id' => $request->input('student_id'),
            'item_id' => $request->input('item_id'),

            // Legacy format support
            'student' => $request->input('student', []),
            'dps_case' => $request->input('dps_case'),
            'prs_case' => $request->input('prs_case')
        ];

        $result = $repository->storeBps($data);

        return response()->json($result);
    }

    public function deleteBps()
    {
        $bpsId =  request('bpsId');
        DisciplineRecord::destroy($bpsId);
    }
}
