<?php

 namespace App\Library\Repository;
 use App\Library\Helper\AcademicHelper;
 use Illuminate\Support\Facades\DB;
 use Illuminate\Database\Eloquent\ModelNotFoundException;
 use App\Models\User;
 use App\Models\MobileDevice;
 use App\Models\StudentStorage;
 use App\Models\StudentStorageGlobal;
 use App\Models\SchoolBusData;
 use App\Models\GpsTrack;
 use App\Models\StudentPickupRequest;
 use Illuminate\Support\Str;
 use App\Models\Branch;
 use App\Library\Helper\GeneralHelper;
 use App\Models\Assessment;
 use App\Models\FormativeAssessmentData;
 use App\Models\SummativeAssessmentData;
 use Carbon\Carbon;
 use App\Models\StudentInformation;
 use App\Models\ElectiveGradeStudent;
 use App\Models\Timetable;
 use App\Models\StudentClassAttendance;
 use App\Models\DisciplineRecord;
 use App\Models\HomeworkDetail;
 use App\Models\AcademicYear;

 class MobileApiRepository {
    protected $ah;

    public function __construct() {
        $this->ah = new AcademicHelper();
    }

    public function studentFiles($authCode) {
        $fileData = null;
        $device = MobileDevice::where('auth_code', $authCode)->first();
        if($device) {
            $userInfo = User::find($device->student_id);
            $globalFiles = StudentStorageGlobal::where('branch_id', $userInfo->branch_id)->get();
            $studentFiles = StudentStorage::where('student_id', $device->student_id)->get();
            $counter = 1;
            foreach ($studentFiles as $key => $fl) {
                $fileData .= $fl->file_name."|*|https://sis.bfi.edu.mm/".$fl->file_url."/*/";
            }
                  foreach ($globalFiles as $key => $fl) {
                $fileData .= $fl->file_name."|*|https://sis.bfi.edu.mm/".$fl->file_url."/*/";
            }
        }
        return $fileData;
    }

    public function checkDeviceData($data) {
        // $ids = explode("/", $data['authCodes']);
        // $allDeviceData = MobileDevice::where('device_token', $data['deviceToken'])
        //                              ->get();
        //
        // foreach ($allDeviceData as $key => $token) {
        //     $isLogged = 0;
        //     foreach ($ids as $id) {
        //         if($id == $token->student_id) {
        //             $isLogged++;
        //         }
        //     }
        //
        //     if($isLogged == 0) {
        //         $deleteData = MobileDevice::destroy('$token->mobile_device_id');
        //     }
        // }
    }

    public function removeUserFromDevice($data) {
        return MobileDevice::where('device_token', $data['deviceToken'])
                    ->where('student_id', $data['userId'])
                    ->delete();
    }

    public function getStudentTracking($authCode) {
        $rd = null;
        $device = MobileDevice::where('auth_code', $authCode)->first();
        if($device) {
            $trackData = SchoolBusData::where('student_id', $device->student_id)->first();
            if($trackData) {
                $lon = $trackData->home_longtitude;
                $lat = $trackData->home_latitude;

                $busTrackData = GpsTrack::where('id_device', $trackData->schoolbus_id)
                                        ->orderBy('id_track', 'desc')
                                        ->first();

                if($busTrackData) {
                    $rd = 'ok|'.$lat.'|'.$lon.'|'.$busTrackData->latitude.'|'.$busTrackData->longitude;
                } else {
                    $rd = 'fail|';
                }
            } else {
                $rd = 'fail|';
            }
        } else {
            $rd = 'fail|';
        }
        return $rd;
    }

    public function mobileNotifications($userIDS) {
        $arr = explode("|", $userIDS);

        $notifications = \DB::table('api_notifications')
                        ->select('notification_body', 'created_at', 'notification_title')
                        ->whereIn('user_id', $arr)
                        // ->orWhere('notification_type', 'all')
                        // ->groupBy('notification_uid')
                        ->groupBy('notification_body', 'created_at', 'notification_title')
                        ->orderBy('created_at', 'desc')
                        ->take(50)
                        ->get();

        $myObj = new \stdClass;
        $jsonString = null;
        $counter = 0;
        foreach ($notifications as $key => $notification) {
            $notificationTimeArr = explode(" ", $notification->created_at);
            $myObj->notificationTitle = $notification->notification_title;
            $myObj->notificationBody = $notification->notification_body;
            $myObj->notificationDate = $notificationTimeArr[0];
            $myObj->notificationTime = $notificationTimeArr[1];
            $myObj->notificationType = null;
            $myJson = json_encode($myObj);
            $jsonString = $jsonString.$myJson;
            $counter++;
        }

        if($counter == 0) {
            $myObj->notificationTitle = "No new notifications";
            $myObj->notificationBody = "We could not find any new notifications.";
            $myObj->notificationDate = " ";
            $myObj->notificationTime = " ";
            $myObj->notificationType = "all";
            $myJson = json_encode($myObj);
            $jsonString = $jsonString.$myJson;
        }

        $jsonString = str_replace("}{","},{",$jsonString);
        $jsonString = "[".$jsonString."]";
        return $jsonString;
    }

    public function createPickupRequest($data) {
        $rd = '';

        $carbonDate = Carbon::now();

        $gh = new GeneralHelper();

        $device = MobileDevice::where('auth_code', $data['authCode'])->first();
        if($device) {

          $userInfo = User::find($device->student_id);
          $branchInfo = Branch::Find($userInfo->branch_id);

          $branchLocation = $branchInfo->gps_location;
          $branchLocArr = explode(",", $branchLocation);

          $distance = $gh->getGpsDistance($branchLocArr[0], $branchLocArr[1], $data['lat'], $data['lon']);

          if($distance <= 150) {

            StudentPickupRequest::firstOrCreate(
              [
                'request_date'    => $carbonDate->format('Y-m-d'),
                'student_id'      => $device->student_id,
                'branch_id'       => $device->student->branch_id,
              ],
              [
                'request_status'  => 0,
                'uuid'              => Str::uuid(),
                'parent_distance'   => $distance." m"
              ]
          );
            $rd = 'ok| Your request recorded for student: ['.$device->student->name.']';
          } else {
            $rd = 'fail|You are too far from campus. Please make request when you are at least 150 meters close to campus! ['.$distance.'m]';
          }
        } else {
            $rd = 'fail|Invalid Auth Code!';
        }
        return $rd;
    }

    public function getStudentTimetable($authCode) {
        $ah = new AcademicHelper();
        $device = MobileDevice::where('auth_code', $authCode)->first();

        if (!$device) {
            return response()->json(['error' => 'Invalid authentication code'], 401);
        }

        $branch = StudentInformation::where("id", $device->student_id)->first();
        $courses = ElectiveGradeStudent::select('grade_id')
            ->distinct('grade_id')
            ->where('student_id', $device->student_id)
            ->get();

        $grades = [];
        foreach ($courses as $course) {
            $grades[] = $course->grade_id;
        }

        $timetable = Timetable::with(['user:id,name', 'subject:subject_id,subject_name'])
            ->where('academic_year_id', $ah->branchAcademicYear($branch->branch_id))
            ->where('branch_id', $device->student->branch_id)
            ->whereIn('grade_id', $grades)
            ->get()
            ->groupBy('week_day');

        return response()->json($timetable);
    }

    public function getStudentAttendanceData($authCode) {
        $device = MobileDevice::where('auth_code', $authCode)->first();

        if (!$device) {
            return response()->json(['error' => 'Invalid authentication code'], 401);
        }

        // Get student branch information
        $branch = StudentInformation::where("id", $device->student_id)->first();

        if (!$branch) {
            return response()->json(['error' => 'Student information not found'], 404);
        }

        // Get current academic year for the branch
        $academicYearId = $this->ah->branchAcademicYear($branch->branch_id);

        if (!$academicYearId) {
            return response()->json(['error' => 'Academic year not found for this branch'], 404);
        }

        // Get academic year start date
        $academicYear = AcademicYear::where('academic_year_id', $academicYearId)->first();

        if (!$academicYear) {
            return response()->json(['error' => 'Academic year details not found'], 404);
        }

        // Get attendance data from academic year start date to current date
        $startDate = $academicYear->start_date;
        $currentDate = date('Y-m-d');

        $attendanceData = StudentClassAttendance::leftJoin('academic_elective_grade', 'academic_elective_grade.grade_id', 'students_attendance_class.grade_id')
            ->where('date', '>=', $startDate)
            ->where('date', '<=', $currentDate)
            ->where('student_id', $device->student_id)
            ->where('academic_year_id', $academicYearId)
            ->orderBy('date', 'desc')
            ->orderBy('week_time', 'asc')
            ->get();

        $formattedData = [];
        foreach ($attendanceData as $item) {
            $timestamp = strtotime($item->date);
            $weekday = date("l", $timestamp);

            $formattedData[] = [
                'date' => $item->date,
                'weekday' => $weekday,
                'subject' => $item->grade_name,
                'period' => $item->week_time,
                'status' => strtoupper($item->attendance_status),
                'attendance_note' => $item->attendance_note ?? null
            ];
        }

        return response()->json([
            'success' => true,
            'data' => $formattedData,
            'total_records' => count($formattedData),
            'academic_year_start' => $startDate,
            'current_date' => $currentDate
        ]);
    }

     public function getStudentBpsData($authCode) {
             $device = MobileDevice::where('auth_code', $authCode)->first();

             if (!$device) {
                 return response()->json(['error' => 'Invalid authentication code'], 401);
             }

             $branch = StudentInformation::where("id", $device->student_id)->first();

             if (!$branch) {
                 return response()->json(['error' => 'Student information not found'], 404);
             }

             $records = DisciplineRecord::leftJoin('discipline_bps_items', 'discipline_bps_items.discipline_item_id', 'item_id')
                 ->leftJoin('users', 'users.id', 'discipline_bps_records.user_id')
                 ->where('student_id', $device->student_id)
                 ->where('academic_year_id', $this->ah->branchAcademicYear($branch->branch_id))
                 ->where('discipline_bps_records.status', 1)
                 ->orderBy('date', 'desc')
                 ->select(
                     'discipline_bps_records.*',
                     'discipline_bps_items.item_title',
                     'discipline_bps_items.item_point',
                     'users.name as teacher_name'
                 )
                 ->get();

             $formattedData = [];
             foreach ($records as $record) {
                 $formattedData[] = [
                     'id' => $record->discipline_record_id,
                     'item_title' => $record->item_title,
                     'item_type' => strtoupper($record->item_type),
                     'item_point' => $record->item_point,
                     'date' => $record->date,
                     'note' => $record->note ?? '',
                     'teacher_name' => $record->teacher_name ?? 'N/A',
                     'status' => $record->status
                 ];
             }

             // Get detention records for the student
             $detentionRecords = \App\Models\DetentionRecord::leftJoin('discipline_bps_records', 'detention_records.dps_record_id', 'discipline_bps_records.discipline_record_id')
                 ->leftJoin('discipline_bps_items', 'discipline_bps_items.discipline_item_id', 'discipline_bps_records.item_id')
                 ->leftJoin('users', 'users.id', 'discipline_bps_records.user_id')
                 ->where('detention_records.student_id', $device->student_id)
                 ->where('detention_records.academic_year_id', $this->ah->branchAcademicYear($branch->branch_id))
                 ->orderBy('detention_records.date', 'desc')
                 ->select(
                     'detention_records.*',
                     'discipline_bps_items.item_title',
                     'discipline_bps_items.item_point',
                     'users.name as teacher_name'
                 )
                 ->get();

             $formattedDetentionData = [];
             foreach ($detentionRecords as $detention) {
                 $formattedDetentionData[] = [
                     'id' => $detention->detention_record_id,
                     'type' => 'DETENTION',
                     'detention_type' => $detention->detention_type,
                     'served_detention_type' => $detention->served_detention_type,
                     'is_served' => $detention->is_served,
                     'system_note' => $detention->system_note,
                     'item_title' => $detention->item_title ?? 'Detention Record',
                     'item_point' => $detention->item_point ?? 0,
                     'latest_point' => $detention->latest_point,
                     'date' => $detention->date,
                     'teacher_name' => $detention->teacher_name ?? 'System',
                     'academic_semester' => $detention->academic_semester
                 ];
             }

             // Calculate total points
             $totalPoints = $records->sum('item_point');

             return response()->json([
                 'success' => true,
                 'bps_records' => $formattedData,
                 'detention_records' => $formattedDetentionData,
                 'total_bps_records' => count($formattedData),
                 'total_detention_records' => count($formattedDetentionData),
                 'total_points' => $totalPoints
             ]);
         }

     public function getStudentHomeworkData($authCode) {
             $ah = new AcademicHelper();
             $device = MobileDevice::where('auth_code', $authCode)->first();

             if (!$device) {
                 return response()->json(['error' => 'Invalid authentication code'], 401);
             }

             $branch = StudentInformation::where("id", $device->student_id)->first();

             if (!$branch) {
                 return response()->json(['error' => 'Student information not found'], 404);
             }

         $homeworks = HomeworkDetail::leftJoin('academic_homework', 'academic_homework.homework_id', 'academic_homework_detail.homework_id')
                 ->where('academic_homework.academic_year_id', $ah->branchAcademicYear($branch->branch_id))
                 ->where('academic_homework.branch_id', $branch->branch_id)
                 ->where('academic_homework_detail.student_id', $device->student_id)
                 ->orderBy('academic_homework.homework_id', 'desc')
                 ->get();

             $formattedData = [];
             foreach ($homeworks as $homework) {
                 $formattedData[] = [
                     'id' => $homework->detail_id,
                     'title' => $homework->title,
                     'subject' => $homework->homework->grade->grade_name ?? 'N/A',
                     'teacher_name' => $homework->homework->teacher->name ?? 'N/A',
                     'homework_data' => $homework->homework_data,
                     'deadline' => $homework->deadline,
                     'uuid' => $homework->uuid
                 ];
             }

             // Calculate completion statistics
             $totalHomeworks = count($formattedData);
             $completedHomeworks = collect($formattedData)->where('is_completed', 1)->count();
             $pendingHomeworks = $totalHomeworks - $completedHomeworks;

             return response()->json([
                 'success' => true,
                 'data' => $formattedData,
                 'total_records' => $totalHomeworks,
                 'completed_count' => $completedHomeworks,
                 'pending_count' => $pendingHomeworks,
                 'completion_percentage' => $totalHomeworks > 0 ? round(($completedHomeworks / $totalHomeworks) * 100, 1) : 0
             ]);
         }

     public function getStudentGrades($authCode) {
             $device = MobileDevice::where('auth_code', $authCode)->first();

             if (!$device) {
                 return response()->json(['error' => 'Invalid authentication code'], 401);
             }

             $branch = StudentInformation::where("id", $device->student_id)->first();

             if (!$branch) {
                 return response()->json(['error' => 'Student information not found'], 404);
             }

             $userId = $device->student_id;
             $academicYearId = $this->ah->branchAcademicYear($branch->branch_id);

             if (!$academicYearId) {
                 return response()->json(['error' => 'Academic year not found for this branch'], 404);
             }

             $summativeList = SummativeAssessmentData::leftJoin('academic_summative_assessments', 'academic_summative_assessments.assessment_id', 'academic_summative_assessments_data.assessment_id')
                                                 ->leftJoin('subjects', 'subjects.subject_id', 'academic_summative_assessments.subject_id')
                                                 ->leftJoin('users', 'users.id', 'academic_summative_assessments.teacher_id')
                                                 ->leftJoin('skills_strands', 'skills_strands.skill_strand_id', 'academic_summative_assessments.strand')
                                                 ->where('academic_summative_assessments.academic_year_id', $academicYearId)
                                                 ->where('academic_summative_assessments_data.score', '>=', 0)
                                                 ->where('academic_summative_assessments_data.student_id', $userId)
                                                 ->orderBy('academic_summative_assessments.created_at', 'desc')
                                                 ->select(
                                                     'academic_summative_assessments.assessment_id',
                                                     'academic_summative_assessments.date',
                                                     'subjects.subject_name',
                                                     'academic_summative_assessments.assessment_name',
                                                     'academic_summative_assessments_data.type_title',
                                                     'academic_summative_assessments_data.score',
                                                     'academic_summative_assessments_data.score_percentage',
                                                     'academic_summative_assessments.strand as strand_id',
                                                     'skills_strands.value as strand_name',
                                                     'users.name as teacher_name'
                                                 )
                                                 ->get();

             $formativeList = FormativeAssessmentData::select(
                                                             'academic_formative_assessments.formative_assessment_id as assessment_id',
                                                             'academic_formative_assessments_data.t1 as tt1',
                                                             'academic_formative_assessments_data.t2 as tt2',
                                                             'academic_formative_assessments_data.t3 as tt3',
                                                             'academic_formative_assessments_data.t4 as tt4',
                                                             'users.name as teacher_name',
                                                             'subjects.subject_name',
                                                             'academic_formative_assessments.assessment_name',
                                                             'academic_formative_assessments.strand as strand_id',
                                                             'strand_info.value as strand_name',
                                                             'academic_formative_assessments.skill as skill_id',
                                                             'skill_info.value as skill_name',
                                                             'academic_formative_assessments.date'
                                                         )
                                                 ->leftJoin('academic_formative_assessments', 'academic_formative_assessments.formative_assessment_id', 'academic_formative_assessments_data.formative_assessment_id')
                                                 ->leftJoin('subjects', 'subjects.subject_id', 'academic_formative_assessments.subject_id')
                                                 ->leftJoin('users', 'users.id', 'academic_formative_assessments.teacher_id')
                                                 ->leftJoin('skills_strands as strand_info', 'strand_info.skill_strand_id', 'academic_formative_assessments.strand')
                                                 ->leftJoin('skills_strands as skill_info', 'skill_info.skill_strand_id', 'academic_formative_assessments.skill')
                                                 ->where('academic_formative_assessments.academic_year_id', $academicYearId)
                                                 ->where('academic_formative_assessments_data.student_id', $userId)
                                                 ->orderBy('academic_formative_assessments.created_at', 'desc')
                                                 ->get();

             return response()->json([
                 'summative' => $summativeList,
                 'formative' => $formativeList
             ]);
         }
     public function getTeacherTimetableData($authCode) {
             $device = MobileDevice::where('auth_code', $authCode)->first();

             if (!$device) {
                 return response()->json(['error' => 'Invalid authentication code'], 401);
             }

             // Check if device is for staff
             if ($device->user_type !== 'staff') {
                 return response()->json(['error' => 'Access denied. Staff authentication required'], 403);
             }

             $userId = $device->student_id; // This is actually staff ID for teacher devices
             $branches = \DB::table('users_branches')
                 ->leftJoin('branches', 'branches.branch_id', 'users_branches.branch_id')
                 ->where('user_id', $userId)
                 ->get();

             if ($branches->isEmpty()) {
                 return response()->json(['error' => 'No branches found for teacher'], 404);
             }

             $today = date('Y-m-d');
             $branchesData = [];

             // Get global academic year info (active academic year)
             $globalAcademicYear = AcademicYear::where('is_active', 1)->first();
             $globalAcademicYearInfo = null;
             if ($globalAcademicYear) {
                 $globalAcademicYearInfo = [
                     'academic_year_id' => $globalAcademicYear->academic_year_id,
                     'academic_year' => $globalAcademicYear->academic_year,
                     'start_date' => $globalAcademicYear->start_date,
                     'end_date' => $globalAcademicYear->end_date,
                     'is_active' => $globalAcademicYear->is_active
                 ];
             }

             foreach ($branches as $branch) {
                 $branchId = $branch->branch_id;
                 $academicYearId = $this->ah->branchAcademicYear($branchId);

                 if (!$academicYearId) {
                     continue; // Skip branches without academic year
                 }

                 // Get academic year details
                 $academicYear = AcademicYear::where('academic_year_id', $academicYearId)->first();
                 $academicYearInfo = null;
                 if ($academicYear) {
                     $academicYearInfo = [
                         'academic_year_id' => $academicYear->academic_year_id,
                         'academic_year' => $academicYear->academic_year,
                         'start_date' => $academicYear->start_date,
                         'end_date' => $academicYear->end_date,
                         'is_active' => $academicYear->is_active
                     ];
                 }

                 // Get current week info
                 $weekInfo = \DB::table('academic_week')
                     ->where('start_date', '<=', $today)
                     ->where('end_date', '>=', $today)
                     ->where('branch_id', $branchId)
                     ->first();

                 $currentWeek = $weekInfo ? $weekInfo->week : null;

                 // Get timetable with attendance status
                 $timetableQuery = \DB::table('academic_timetable')
                     ->selectRaw('
                         academic_timetable.timetable_id,
                         academic_timetable.week_day,
                         academic_timetable.week_time,
                         academic_timetable.grade_id,
                         academic_timetable.subject_id,
                         subjects.subject_name,
                         academic_elective_grade.grade_name,
                         count(students_attendance_class.class_attendance_id) as attendance_count
                     ')
                     ->leftJoin('subjects', 'subjects.subject_id', 'academic_timetable.subject_id')
                     ->leftJoin('academic_elective_grade', 'academic_elective_grade.grade_id', 'academic_timetable.grade_id')
                     ->leftJoin('students_attendance_class', function($join) use($academicYearId, $currentWeek) {
                         $join->where('students_attendance_class.academic_year_id', $academicYearId);
                         $join->on('students_attendance_class.week_day', 'academic_timetable.week_day');
                         $join->on('students_attendance_class.week_time', 'academic_timetable.week_time');
                         $join->on('students_attendance_class.grade_id', 'academic_timetable.grade_id');
                         $join->on('students_attendance_class.subject_id', 'academic_timetable.subject_id');
                         if ($currentWeek) {
                             $join->where('students_attendance_class.week', $currentWeek);
                         }
                     })
                     ->where('academic_timetable.academic_year_id', $academicYearId)
                     ->where('academic_timetable.branch_id', $branchId)
                     ->where('academic_timetable.user_id', $userId)
                     ->groupBy([
                         'academic_timetable.timetable_id',
                         'academic_timetable.week_day',
                         'academic_timetable.week_time',
                         'academic_timetable.grade_id',
                         'academic_timetable.subject_id',
                         'subjects.subject_name',
                         'academic_elective_grade.grade_name'
                     ])
                     ->orderBy('week_day', 'ASC')
                     ->orderBy('week_time', 'ASC')
                     ->get();

                 $formattedTimetable = [];
                 foreach ($timetableQuery as $item) {
                     $formattedTimetable[] = [
                         'timetable_id' => $item->timetable_id,
                         'week_day' => $item->week_day,
                         'week_time' => $item->week_time,
                         'grade_id' => $item->grade_id,
                         'subject_id' => $item->subject_id,
                         'subject_name' => $item->subject_name ?? 'Unknown Subject',
                         'grade_name' => $item->grade_name ?? 'Unknown Grade',
                         'attendance_taken' => $item->attendance_count > 0,
                         'attendance_count' => $item->attendance_count,
                         'current_week' => $currentWeek
                     ];
                 }

                 $branchesData[] = [
                     'branch_id' => $branchId,
                     'branch_name' => $branch->branch_name,
                     'academic_year_id' => $academicYearId,
                     'academic_year_info' => $academicYearInfo,
                     'current_week' => $currentWeek,
                     'timetable' => $formattedTimetable
                 ];
             }

             return response()->json([
                 'success' => true,
                 'teacher_id' => $userId,
                 'global_academic_year' => $globalAcademicYearInfo,
                 'branches' => $branchesData,
                 'total_branches' => count($branchesData)
             ]);
         }

         public function getTeacherBpsData($authCode) {
             $device = MobileDevice::where('auth_code', $authCode)->first();

             if (!$device) {
                 return response()->json(['error' => 'Invalid authentication code'], 401);
             }

             // Check if device is for staff
             if ($device->user_type !== 'staff') {
                 return response()->json(['error' => 'Access denied. Staff authentication required'], 403);
             }

             $userId = $device->student_id; // This is actually staff ID for teacher devices
             $branches = \DB::table('users_branches')
                 ->leftJoin('branches', 'branches.branch_id', 'users_branches.branch_id')
                 ->where('user_id', $userId)
                 ->get();

             if ($branches->isEmpty()) {
                 return response()->json(['error' => 'No branches found for teacher'], 404);
             }

             // Get discipline items
             $dpsItems = \DB::table('discipline_bps_items')
                 ->where('item_status', 1)
                 ->where('item_type', 'dps')
                 ->select('discipline_item_id', 'item_title', 'item_point', 'item_type')
                 ->get();

             $prsItems = \DB::table('discipline_bps_items')
                 ->where('item_status', 1)
                 ->where('item_type', 'prs')
                 ->select('discipline_item_id', 'item_title', 'item_point', 'item_type')
                 ->get();

             // Include CSRF token in the response
             $csrfToken = csrf_token();

             $branchesData = [];

             // Get global academic year info (active academic year)
             $globalAcademicYear = AcademicYear::where('is_active', 1)->first();
             $globalAcademicYearInfo = null;
             if ($globalAcademicYear) {
                 $globalAcademicYearInfo = [
                     'academic_year_id' => $globalAcademicYear->academic_year_id,
                     'academic_year' => $globalAcademicYear->academic_year,
                     'start_date' => $globalAcademicYear->start_date,
                     'end_date' => $globalAcademicYear->end_date,
                     'is_active' => $globalAcademicYear->is_active
                 ];
             }

             foreach ($branches as $branch) {
                 $branchId = $branch->branch_id;
                 $academicYearId = $this->ah->branchAcademicYear($branchId);

                 if (!$academicYearId) {
                     continue; // Skip branches without academic year
                 }

                 // Get academic year details
                 $academicYear = AcademicYear::where('academic_year_id', $academicYearId)->first();
                 $academicYearInfo = null;
                 if ($academicYear) {
                     $academicYearInfo = [
                         'academic_year_id' => $academicYear->academic_year_id,
                         'academic_year' => $academicYear->academic_year,
                         'start_date' => $academicYear->start_date,
                         'end_date' => $academicYear->end_date,
                         'is_active' => $academicYear->is_active
                     ];
                 }

                 // Get students for this branch - FIXED: removed user_id column
                 $students = \DB::table('users')
                     ->select([
                         'users.id as student_id',
                         'users.name',
                         'classrooms.classroom_name'
                     ])
                     ->leftJoin('students_classroom', function($join) use($academicYearId, $branchId) {
                         $join->on('students_classroom.student_id', 'users.id')
                             ->where('students_classroom.branch_id', $branchId)
                             ->where('students_classroom.academic_year_id', $academicYearId);
                     })
                     ->leftJoin('classrooms', 'classrooms.classroom_id', 'students_classroom.classroom_id')
                     ->where('users.user_type', 'student')
                     ->where('users.user_status', 1)
                     ->where('users.branch_id', $branchId)
                     ->orderBy('classrooms.classroom_name')
                     ->orderBy('users.name')
                     ->get();

                 // Get BPS records created by this teacher
                 $bpsRecords = \DB::table('discipline_bps_records')
                     ->select([
                         'discipline_bps_records.discipline_record_id',
                         'discipline_bps_records.student_id',
                         'discipline_bps_records.item_type',
                         'discipline_bps_records.date',
                         'discipline_bps_records.note',
                         'users.name as student_name',
                         'discipline_bps_items.item_title',
                         'discipline_bps_items.item_point',
                         'classrooms.classroom_name'
                     ])
                     ->leftJoin('users', 'users.id', 'discipline_bps_records.student_id')
                     ->leftJoin('discipline_bps_items', 'discipline_bps_items.discipline_item_id', 'discipline_bps_records.item_id')
                     ->leftJoin('students_classroom', function($join) use($academicYearId, $branchId) {
                         $join->on('students_classroom.student_id', 'discipline_bps_records.student_id')
                             ->where('students_classroom.branch_id', $branchId)
                             ->where('students_classroom.academic_year_id', $academicYearId);
                     })
                     ->leftJoin('classrooms', 'classrooms.classroom_id', 'students_classroom.classroom_id')
                     ->where('discipline_bps_records.status', 1)
                     ->where('discipline_bps_records.academic_year_id', $academicYearId)
                     ->where('discipline_bps_records.branch_id', $branchId)
                     ->where('discipline_bps_records.user_id', $userId)
                     ->where('users.user_status', 1)
                     ->orderBy('discipline_bps_records.discipline_record_id', 'desc')
                     ->get();

                 $branchesData[] = [
                     'branch_id' => $branchId,
                     'branch_name' => $branch->branch_name,
                     'academic_year_id' => $academicYearId,
                     'academic_year_info' => $academicYearInfo,
                     'students' => $students,
                     'bps_records' => $bpsRecords,
                     'total_students' => count($students),
                     'total_bps_records' => count($bpsRecords)
                 ];
             }

             return response()->json([
                 'success' => true,
                 'teacher_id' => $userId,
                 'global_academic_year' => $globalAcademicYearInfo,
                 'branches' => $branchesData,
                 'discipline_items' => [
                     'dps_items' => $dpsItems,
                     'prs_items' => $prsItems
                 ],
                 'csrf_token' => $csrfToken,
                 'total_branches' => count($branchesData)
             ]);
         }
         public function getAttendanceDetails($authCode, $timetableId) {
                 try {
                     $device = MobileDevice::where('auth_code', $authCode)->first();

                     if (!$device) {
                         return response()->json(['error' => 'Invalid authentication code'], 401);
                     }

                     // Check if device is for staff
                     if ($device->user_type !== 'staff') {
                         return response()->json(['error' => 'Access denied. Staff authentication required'], 403);
                     }

                     $userId = $device->student_id; // This is actually staff ID for teacher devices

                     // Get timetable information
                     $timetable = \DB::table('academic_timetable')
                         ->select([
                             'academic_timetable.*',
                             'subjects.subject_name',
                             'academic_elective_grade.grade_name',
                             'branches.branch_name'
                         ])
                         ->leftJoin('subjects', 'subjects.subject_id', 'academic_timetable.subject_id')
                         ->leftJoin('academic_elective_grade', 'academic_elective_grade.grade_id', 'academic_timetable.grade_id')
                         ->leftJoin('branches', 'branches.branch_id', 'academic_timetable.branch_id')
                         ->where('academic_timetable.timetable_id', $timetableId)
                         ->where('academic_timetable.user_id', $userId)
                         ->first();

                     if (!$timetable) {
                         return response()->json(['error' => 'Timetable not found or access denied'], 404);
                     }

                 $branchId = $timetable->branch_id;
                 $academicYearId = $timetable->academic_year_id;
                 $gradeId = $timetable->grade_id;
                 $subjectId = $timetable->subject_id;
                 $weekDay = $timetable->week_day;
                 $weekTime = $timetable->week_time;

                 // Get current week
                 $today = date('Y-m-d');
                 $weekInfo = \DB::table('academic_week')
                     ->where('start_date', '<=', $today)
                     ->where('end_date', '>=', $today)
                     ->where('branch_id', $branchId)
                     ->where('academic_year_id', $academicYearId)
                     ->first();

                 $currentWeek = $weekInfo ? $weekInfo->week : null;

                 // Calculate the date for the specific weekday
                 $currentDate = new \DateTime();
                 $currentDayOfWeek = $currentDate->format('N'); // 1 (Monday) to 7 (Sunday)
                 $daysToAdd = ($weekDay - $currentDayOfWeek) % 7;
                 if ($daysToAdd < 0) {
                     $daysToAdd += 7;
                 }
                 $selectedDate = clone $currentDate;
                 $selectedDate->add(new \DateInterval('P' . $daysToAdd . 'D'));
                 $attendanceDate = $selectedDate->format('Y-m-d');

                 // First, get all students for this grade (no duplicates)
                 $students = ElectiveGradeStudent::select([
                         'academic_elective_grade_students.student_id',
                         'users.name as student_name',
                         'users.photo as student_photo'
                     ])
                     ->leftJoin('users', 'users.id', 'academic_elective_grade_students.student_id')
                     ->where('academic_elective_grade_students.grade_id', $gradeId)
                     ->where('users.user_status', 1)
                     ->orderBy('users.name', 'ASC')
                     ->get();

                 // Get attendance data separately to avoid duplicates
                 $attendanceData = \DB::table('students_attendance_class')
                     ->select([
                         'student_id',
                         'attendance_status',
                         'attendance_note'
                     ])
                     ->where('subject_id', $subjectId)
                     ->where('week_day', $weekDay)
                     ->where('week_time', $weekTime)
                     ->where('grade_id', $gradeId);

                 if ($currentWeek) {
                     $attendanceData = $attendanceData->where('week', $currentWeek);
                 }

                 $attendanceData = $attendanceData->get()->keyBy('student_id');

                 // Get classroom information separately for each student
                 $studentClassrooms = \DB::table('students_classroom')
                     ->leftJoin('classrooms', 'classrooms.classroom_id', 'students_classroom.classroom_id')
                     ->where('students_classroom.academic_year_id', $academicYearId)
                     ->where('students_classroom.branch_id', $branchId)
                     ->select('students_classroom.student_id', 'classrooms.classroom_name')
                     ->get()
                     ->keyBy('student_id');

                 // Format student data
                 $formattedStudents = [];
                 foreach ($students as $student) {
                     $attendanceInfo = $attendanceData->get($student->student_id);
                     $attendanceStatus = $attendanceInfo ? $attendanceInfo->attendance_status : 'not_taken';
                     $attendanceNote = $attendanceInfo ? $attendanceInfo->attendance_note : '';
                     $classroomInfo = $studentClassrooms->get($student->student_id);

                     $formattedStudents[] = [
                         'student_id' => $student->student_id,
                         'student_name' => $student->student_name ?? 'Unknown Student',
                         'student_photo' => $student->student_photo,
                         'classroom_name' => $classroomInfo ? $classroomInfo->classroom_name : 'No Classroom',
                         'attendance_status' => $attendanceStatus,
                         'attendance_note' => $attendanceNote,
                         'attendance_options' => [
                             'present' => $attendanceStatus === 'present',
                             'late' => $attendanceStatus === 'late',
                             'absent' => $attendanceStatus === 'absent'
                         ]
                     ];
                 }

                     return response()->json([
                         'success' => true,
                         'timetable_info' => [
                             'timetable_id' => $timetable->timetable_id,
                             'subject_name' => $timetable->subject_name ?? 'Unknown Subject',
                             'grade_name' => $timetable->grade_name ?? 'Unknown Grade',
                             'branch_name' => $timetable->branch_name ?? 'Unknown Branch',
                             'week_day' => $weekDay,
                             'week_time' => $weekTime,
                             'current_week' => $currentWeek,
                             'attendance_date' => $attendanceDate
                         ],
                         'students' => $formattedStudents,
                         'total_students' => count($formattedStudents),
                         'attendance_summary' => [
                             'present_count' => count(array_filter($formattedStudents, function($s) { return $s['attendance_status'] === 'present'; })),
                             'late_count' => count(array_filter($formattedStudents, function($s) { return $s['attendance_status'] === 'late'; })),
                             'absent_count' => count(array_filter($formattedStudents, function($s) { return $s['attendance_status'] === 'absent'; })),
                             'not_taken_count' => count(array_filter($formattedStudents, function($s) { return $s['attendance_status'] === 'not_taken'; }))
                         ]
                     ]);
                 } catch (\Exception $e) {
                     return response()->json([
                         'error' => 'Server error occurred',
                         'message' => $e->getMessage(),
                         'line' => $e->getLine(),
                         'file' => basename($e->getFile())
                     ], 500);
                 }
             }
         public function storeBps($data) {
             $branchId = $data['branch_id'];
             $caseType = $data['case_type']; // Corresponds to 'item_type'
             $date = $data['date'];
             $note = $data['note'] ?? '';
             $userId = $data['user_id'];
             $ah = new AcademicHelper();

             // New fields from $data, assuming they are global for this batch
             $academicSemester = $data['academic_semester'] ?? null;
             $itemTitle = $data['item_title'] ?? null;
             $itemPoint = $data['item_point'] ?? null;

             $academicYearId = $ah->branchAcademicYear($branchId);
             $results = [];
             $successCount = 0;
             $totalRecords = 0;

             // Handle both single and multiple students
             $students = [];
             if (isset($data['students']) && is_array($data['students'])) {
                 // Multiple students (array format)
                 $students = $data['students'];
             } elseif (isset($data['student_id'])) {
                 // Single student (single ID)
                 $students = [$data['student_id']];
             } elseif (isset($data['student']) && is_array($data['student'])) {
                 // Legacy format from web interface (array of student IDs)
                 $students = $data['student'];
             } else {
                 return [
                     'success' => false,
                     'message' => 'No students provided',
                     'results' => []
                 ];
             }

             // Handle both single and multiple items (item IDs)
             $itemIds = []; // Renamed from $items for clarity
             if (isset($data['items']) && is_array($data['items'])) {
                 // Multiple items (array format of IDs)
                 $itemIds = $data['items'];
             } elseif (isset($data['item_id'])) {
                 // Single item (single ID)
                 $itemIds = [$data['item_id']];
             } elseif (isset($data['dps_case'])) {
                 // Legacy DPS case format (ID)
                 $itemIds = [$data['dps_case']];
             } elseif (isset($data['prs_case'])) {
                 // Legacy PRS case format (ID)
                 $itemIds = [$data['prs_case']];
             } else {
                 return [
                     'success' => false,
                     'message' => 'No BPS items provided',
                     'results' => []
                 ];
             }

             // Process each student
             foreach ($students as $studentId) {
                 // Process each BPS item for this student
                 foreach ($itemIds as $itemId) { // Iterate over item IDs
                     $totalRecords++;
                     try {
                         $record = new DisciplineRecord();
                         $record->student_id = $studentId;
                         $record->item_id = $itemId; // Current item's ID
                         $record->item_title = $itemTitle; // Set new field
                         $record->item_point = $itemPoint; // Set new field
                         $record->item_type = $caseType; // This is 'item_type'
                         $record->date = $date;
                         $record->note = $note;
                         $record->user_id = $userId;
                         $record->branch_id = $branchId;
                         $record->academic_year_id = $academicYearId;
                         $record->academic_semester = $academicSemester; // Set new field
                         $record->status = 1;
                         $record->save();

                         $successCount++;
                         $results[] = [
                             'student_id' => $studentId,
                             'item_id' => $itemId,
                             'status' => 'success',
                             'record_id' => $record->discipline_record_id
                         ];
                     } catch (\Exception $e) {
                         $results[] = [
                             'student_id' => $studentId,
                             'item_id' => $itemId,
                             'status' => 'error',
                             'message' => $e->getMessage()
                         ];
                     }
                 }
             }

             // Determine if this was single or multiple operation
             $operationType = (count($students) == 1 && count($itemIds) == 1) ? 'single' : 'multiple'; // Use $itemIds

             // Generate fresh CSRF token for next request
             $newCsrfToken = csrf_token();

             return [
                 'success' => $successCount > 0,
                 'message' => $successCount . ' BPS record(s) created successfully out of ' . $totalRecords . ' attempted',
                 'operation_type' => $operationType,
                 'total_students' => count($students),
                 'total_items' => count($itemIds), // Use $itemIds
                 'total_records_attempted' => $totalRecords,
                 'successful_records' => $successCount,
                 'failed_records' => $totalRecords - $successCount,
                 'results' => $results,
                 'csrf_token' => $newCsrfToken, // Fresh token for next request
                 'academic_year_id' => $academicYearId,
                 'timestamp' => date('Y-m-d H:i:s')
             ];
         }
 }

